# CIM-Tracker 项目结构说明

## 🎯 项目概述

CIM-Tracker 是基于 Mamba 架构的多目标跟踪系统，完全替代了传统的 Transformer 架构。

## 📁 核心文件结构

### 🔧 核心模型文件
```
models/
├── cife.py          # CIFE 特征提取模块 (Mamba-based)
├── cdiim.py         # CDIIM 时序建模模块 (替代 Transformer)
├── motr.py          # 主模型文件 (集成 CIFE + CDIIM)
├── backbone.py      # 骨干网络 (ResNet/其他)
├── matcher.py       # 匈牙利匹配器
├── deformable_detr.py # 基础组件 (MLP, 损失函数等)
└── position_encoding.py # 位置编码
```

### 🗂️ 数据处理
```
datasets/
├── __init__.py      # 数据集构建函数
├── transforms.py    # 数据增强 (包含轨迹增强)
├── dance.py         # DanceTrack 数据集
├── joint.py         # 联合数据集
└── samplers.py      # 数据采样器
```

### ⚙️ 配置和工具
```
configs/
└── motrv2.args      # 训练配置文件 (保留作为参考)

tools/
├── launch.py        # 分布式训练启动器
├── visualize.py     # 可视化工具
└── *.sh            # 各种训练和评估脚本
```

### 🔧 核心训练文件
```
main.py              # 主训练脚本
engine.py            # 训练引擎 (包含高效长序列训练)
requirements.txt     # Python 依赖
```

## ❌ 已移除的文件

以下文件已被 CIM-Tracker 的新架构替代：

### 🗑️ 已删除的模块
- `models/deformable_transformer_plus.py` - 被 CDIIM 替代
- `models/qim.py` - 被 CDIIM 替代  
- `models/ops/` - MultiScale Deformable Attention (被 Mamba 替代)

### 🗑️ 已删除的工具
- `tools/batch_diff.py` - 代码比较工具
- `tools/make_detdb.py` - 特定数据集工具
- `test_cife.py` - 开发阶段测试文件

## 🚀 CIM-Tracker 架构优势

### 传统 MOTR 架构
```
Backbone → MSDeformAttn → DeformableTransformer → QIM → Prediction
```

### CIM-Tracker 架构  
```
Backbone → CIFE (Mamba) → CDIIM (Mamba) → Prediction
```

### 🎯 核心改进
1. **线性复杂度**: Mamba 的 O(n) vs Attention 的 O(n²)
2. **端到端 ID 学习**: 可学习的 ID 词典替代启发式匹配
3. **训练鲁棒性**: 轨迹增强 + MaskObs 策略
4. **高效训练**: 选择性梯度计算

## 📋 依赖说明

CIM-Tracker 不再需要：
- ❌ CUDA 扩展编译
- ❌ MultiScaleDeformableAttention 构建
- ❌ 复杂的 C++/CUDA 依赖

只需要标准的 PyTorch 环境！

## 🔄 Git 管理

`.gitignore` 已更新，包含：
- Python 缓存文件
- 模型权重和训练输出
- 数据文件
- IDE 配置
- 临时和调试文件
- 原始 MOTR 遗留文件

## 🎉 项目状态

✅ **完全实现**: 五个阶段全部完成
✅ **测试通过**: 所有模块功能正常
✅ **代码清理**: 移除不需要的文件
✅ **文档完整**: 结构清晰，易于维护
