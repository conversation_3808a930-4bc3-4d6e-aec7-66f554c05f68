# CIM-Tracker 训练指南

## 🎯 概述

本指南将帮助您完成 CIM-Tracker 的训练，包括数据集准备、环境配置和训练执行。

## 📋 准备工作

### 1. 环境要求

```bash
# Python 环境
Python >= 3.8
PyTorch >= 1.9.0
CUDA >= 11.0 (推荐)

# 主要依赖
torch
torchvision
numpy
opencv-python
pillow
einops
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

## 📁 数据集准备

### DanceTrack 数据集下载和组织

#### 步骤 1: 下载 DanceTrack 数据集

```bash
# 官方下载地址
# https://github.com/DanceTrack/DanceTrack

# 或使用 wget (如果有直接链接)
wget https://dancetrack.github.io/dataset/DanceTrack.zip
```

#### 步骤 2: 解压和组织数据集

将下载的数据集按以下结构组织：

```
CIMTracker/
├── data/
│   ├── DanceTrack/
│   │   ├── train/
│   │   │   ├── dancetrack0001/
│   │   │   │   ├── img1/
│   │   │   │   │   ├── 00000001.jpg
│   │   │   │   │   ├── 00000002.jpg
│   │   │   │   │   └── ...
│   │   │   │   └── gt/
│   │   │   │       └── gt.txt
│   │   │   ├── dancetrack0002/
│   │   │   └── ...
│   │   ├── val/
│   │   │   ├── dancetrack0003/
│   │   │   └── ...
│   │   └── test/
│   │       ├── dancetrack0004/
│   │       └── ...
│   ├── crowdhuman/  (可选，用于数据增强)
│   │   ├── annotation_trainval.odgt
│   │   └── Images/
│   └── det_db_motrv2.json  (可选，YOLOX检测结果)
```

#### 步骤 3: 创建数据目录

```bash
# 在项目根目录下创建数据目录
mkdir -p data/DanceTrack

# 将下载的数据集移动到正确位置
# 假设您下载的数据集在 ~/Downloads/DanceTrack
mv ~/Downloads/DanceTrack/* data/DanceTrack/
```

### CrowdHuman 数据集 (可选，用于数据增强)

如果您想使用 CrowdHuman 数据集进行数据增强：

```bash
# 下载 CrowdHuman
mkdir -p data/crowdhuman
# 下载并解压到 data/crowdhuman/
```

## 🔧 预训练权重和检测结果准备

### 1. 下载 DETR 预训练权重

DETR 预训练权重用于初始化 backbone（ResNet）和基础特征提取组件：

```bash
# 创建预训练权重目录
mkdir -p pretrained

# 下载 Deformable DETR 预训练权重
# 从 https://github.com/fundamentalvision/Deformable-DETR 下载
# r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth

# 或使用 wget (如果有直接链接)
cd pretrained
wget https://github.com/fundamentalvision/Deformable-DETR/releases/download/v1.0/r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth
```

### 2. 下载 YOLOX 检测结果（可选，用于增强训练）

YOLOX 检测结果作为额外的 proposals 来增强训练效果：

```bash
# 下载 YOLOX 检测结果
# 从 MOTRv2 官方仓库下载 det_db_motrv2.json
wget https://github.com/megvii-research/MOTRv2/releases/download/v1.0/det_db_motrv2.json

# 放置到数据目录
mv det_db_motrv2.json data/
```

**注意**：YOLOX 检测结果是可选的。如果没有，CIM-Tracker 仍然可以正常训练，只是可能效果略有差异。

## 🚀 开始训练

### 1. 检查配置文件

编辑 `configs/cim_tracker_dancetrack.args`，确保路径正确：

```bash
# 检查数据集路径
--mot_path data/DanceTrack

# 检查预训练权重路径
--pretrained pretrained/r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth

# 检查输出目录
--output_dir outputs/cim_tracker
```

### 2. 单GPU训练

```bash
# 基础训练命令
python main.py $(cat configs/cim_tracker_dancetrack.args)

# 或者指定具体参数
python main.py \
    --meta_arch motr \
    --dataset_file e2e_dance \
    --epochs 20 \
    --batch_size 2 \
    --lr 2e-4 \
    --mot_path data/DanceTrack \
    --pretrained pretrained/r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth \
    --output_dir outputs/cim_tracker
```

### 3. 多GPU训练 (推荐)

```bash
# 使用 tools/train.sh
bash tools/train.sh configs/cim_tracker_dancetrack.args 2

# 或手动启动分布式训练
python -m torch.distributed.launch \
    --nproc_per_node=2 \
    --use_env \
    main.py $(cat configs/cim_tracker_dancetrack.args)
```

## 📊 训练监控

### 查看训练日志

```bash
# 实时查看训练输出
tail -f outputs/cim_tracker/train.log

# 查看损失变化
grep "loss" outputs/cim_tracker/train.log
```

### 检查点管理

训练过程中会自动保存检查点：

```
outputs/cim_tracker/
├── checkpoint.pth          # 最新检查点
├── checkpoint0004.pth      # 第5轮检查点
├── checkpoint0009.pth      # 第10轮检查点
└── ...
```

## 🔧 训练参数调优

### 关键参数说明

```bash
# Mamba 架构参数
--mamba_num_layers 4        # Mamba 层数
--mamba_state_dim 16        # 状态维度
--mamba_expand 2            # 扩展因子

# ID 学习参数
--num_id_vocabulary 50      # ID 词典大小
--id_loss_coef 2.0         # ID 损失权重

# 训练效率参数
--grad_frames 5             # 梯度计算帧数
--batch_size 2              # 批次大小
--sampler_lengths 8         # 序列长度
```

### 常见问题和解决方案

1. **显存不足**：
   ```bash
   # 减少批次大小
   --batch_size 1
   
   # 减少序列长度
   --sampler_lengths 5
   
   # 减少梯度计算帧数
   --grad_frames 3
   ```

2. **训练速度慢**：
   ```bash
   # 增加梯度计算帧数
   --grad_frames 8
   
   # 使用更多GPU
   bash tools/train.sh configs/cim_tracker_dancetrack.args 4
   ```

3. **收敛困难**：
   ```bash
   # 调整学习率
   --lr 1e-4
   --lr_backbone 1e-5
   
   # 调整损失权重
   --id_loss_coef 1.0
   --bbox_loss_coef 10
   ```

## 🎯 训练完成后

### 模型评估

```bash
# 在验证集上评估
python main.py \
    --eval \
    --resume outputs/cim_tracker/checkpoint.pth \
    --mot_path data/DanceTrack
```

### 模型推理

```bash
# 在测试视频上推理
python submit_dance.py \
    --meta_arch motr \
    --resume outputs/cim_tracker/checkpoint.pth \
    --mot_path data/DanceTrack
```

## 📈 预期结果

使用 CIM-Tracker 在 DanceTrack 数据集上训练，预期可以达到：

- **HOTA**: ~55-60%
- **MOTA**: ~85-90%
- **IDF1**: ~60-65%

训练时间（单个 RTX 3090）：
- **20 epochs**: ~8-12 小时
- **收敛**: ~15-20 epochs

## 🆘 故障排除

如果遇到问题，请检查：

1. ✅ 数据集路径是否正确
2. ✅ 预训练权重是否下载
3. ✅ GPU 显存是否足够
4. ✅ 依赖包是否正确安装
5. ✅ CUDA 版本是否兼容

## 📞 获取帮助

如果遇到问题，可以：

1. 检查 `PROJECT_STRUCTURE.md` 了解项目结构
2. 查看 `DETR_YOLOX_EXPLANATION.md` 了解 DETR 和 YOLOX 的关系
3. 调整配置文件中的参数
4. 检查训练日志中的错误信息

## 🚀 **快速开始指南**

### 📋 **关键文件下载链接**

```bash
# 1. DETR 预训练权重（必需）
mkdir -p pretrained
wget -P pretrained/ https://github.com/fundamentalvision/Deformable-DETR/releases/download/v1.0/r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth

# 2. YOLOX 检测结果（可选，但推荐）
wget -P data/ https://github.com/megvii-research/MOTRv2/releases/download/v1.0/det_db_motrv2.json

# 3. DanceTrack 数据集（手动下载）
# 访问: https://github.com/DanceTrack/DanceTrack
# 解压到: data/DanceTrack/
```

### 🎯 **一键训练流程**

```bash
# 1. 下载数据集和权重（见上方链接）

# 2. 自动设置环境
python setup_dataset.py

# 3. 开始训练
python train_cim_tracker.py --num_gpus 1

# 4. 监控训练进度
tail -f outputs/cim_tracker/train.log
```

### 💡 **重要提示**

- **DETR 权重**：用于初始化 backbone，提供特征提取能力
- **YOLOX 检测**：提供额外 proposals，增强训练效果
- **两者结合**：实现最佳性能，详见 `DETR_YOLOX_EXPLANATION.md`
