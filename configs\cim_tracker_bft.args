# CIM-Tracker BFT 训练配置文件
# 基于 Mamba 架构的多目标跟踪系统

# ================== 基础模型配置 ==================
--meta_arch motr
--dataset_file e2e_joint
--epochs 25
--with_box_refine
--lr_drop 20
--lr 1e-4
--lr_backbone 1e-5

# ================== 预训练模型 ==================
# BFT 专用预训练权重
--pretrained D:/Projects/Datasets/CIM_models/r50_deformable_detr_coco_bft.pth

# ================== 训练参数 ==================
--batch_size 1
--sample_mode random_interval
--sample_interval 15
--sampler_lengths 10
--dropout 0.1

# ================== CIM-Tracker 特有参数 ==================
# Mamba 架构参数
--mamba_num_layers 6
--mamba_state_dim 32
--mamba_expand 2
--mamba_conv_dim 4
--id_dim 512

# ID 学习参数
--num_id_vocabulary 200
--id_loss_coef 3.0

# 训练鲁棒性参数
--aug_occlusion_prob 0.2
--aug_switch_prob 0.2
--mask_obs_threshold 0.3

# 高效训练参数
--grad_frames 6

# ================== 查询和检测参数 ==================
--num_queries 50
--query_denoise 0.1

# ================== 损失函数权重 ==================
--cls_loss_coef 2
--bbox_loss_coef 8
--giou_loss_coef 3

# ================== 数据增强 ==================
--append_crowd

# ================== 数据集路径配置 ==================
# BFT 数据集路径
--mot_path data
--det_db D:/Projects/Datasets/CIM_models/det_db_motrv2.json

# ================== 内存优化配置 ==================
# 启用梯度检查点技术以节省显存
--use_checkpoint
--gradient_checkpointing

# ================== 输出配置 ==================
--output_dir outputs/cim_tracker_bft
