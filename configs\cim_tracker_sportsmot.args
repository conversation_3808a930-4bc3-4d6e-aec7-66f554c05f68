# CIM-Tracker SportsMOT 训练配置文件
# 基于 Mamba 架构的多目标跟踪系统

# ================== 基础模型配置 ==================
--meta_arch motr
--dataset_file e2e_joint
--epochs 20
--with_box_refine
--lr_drop 15
--lr 2e-4
--lr_backbone 2e-5

# ================== 预训练模型 ==================
# SportsMOT 专用预训练权重
--pretrained D:/Projects/Datasets/CIM_models/r50_deformable_detr_coco_sportsmot.pth

# ================== 训练参数 ==================
--batch_size 2
--sample_mode random_interval
--sample_interval 10
--sampler_lengths 8
--dropout 0.1

# ================== CIM-Tracker 特有参数 ==================
# Mamba 架构参数
--mamba_num_layers 4
--mamba_state_dim 16
--mamba_expand 2
--mamba_conv_dim 4
--id_dim 256

# ID 学习参数
--num_id_vocabulary 100
--id_loss_coef 2.0

# 训练鲁棒性参数
--aug_occlusion_prob 0.15
--aug_switch_prob 0.15
--mask_obs_threshold 0.4

# 高效训练参数
--grad_frames 5

# ================== 查询和检测参数 ==================
--num_queries 30
--query_denoise 0.05

# ================== 损失函数权重 ==================
--cls_loss_coef 2
--bbox_loss_coef 5
--giou_loss_coef 2

# ================== 数据增强 ==================
--append_crowd

# ================== 数据集路径配置 ==================
# SportsMOT 数据集路径
--mot_path data
--det_db D:/Projects/Datasets/CIM_models/det_db_motrv2.json

# ================== 内存优化配置 ==================
# 启用梯度检查点技术以节省显存
--use_checkpoint
--gradient_checkpointing

# ================== 输出配置 ==================
--output_dir outputs/cim_tracker_sportsmot
