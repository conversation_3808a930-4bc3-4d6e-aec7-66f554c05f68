# 文件: models/cdiim.py

import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange

from .cife import MambaBlock  # 从我们第二阶段创建的cife.py中复用MambaBlock
from .deformable_detr import MLP # 复用MOTR中的MLP

class MambaStateSynchronizer(nn.Module):
    """
    使用多头注意力机制在不同轨迹的Mamba隐状态之间进行信息同步。
    灵感来源: SambaMOTR.samba.SambaBlock.sync_hidden_states
    """
    def __init__(self, d_model, n_head):
        super().__init__()
        self.attention = nn.MultiheadAttention(d_model, n_head, batch_first=True)
        self.norm = nn.LayerNorm(d_model)

    def forward(self, hidden_states):
        """
        Input: hidden_states (K, D_inner, N_state) - K是轨迹数量
        Output: synchronized_states (K, D_inner, N_state)
        """
        # 为了进行注意力计算，我们需要将维度调整为 (K, N_state, D_inner)
        states_for_attn = rearrange(hidden_states, 'k d n -> k n d')
        
        synced_states, _ = self.attention(states_for_attn, states_for_attn, states_for_attn)
        synced_states = self.norm(states_for_attn + synced_states)
        
        # 恢复原始维度
        return rearrange(synced_states, 'k n d -> k d n')

class CDIIM(nn.Module):
    """
    Causal Denoising Interaction and Inference Module.
    这是新的时序处理核心，取代原有的Transformer解码器。
    """
    def __init__(self, d_model=256, n_head=8, num_mamba_layers=4, mamba_state_dim=16,
                 mamba_expand=2, mamba_conv_dim=4, num_id_vocabulary=50, id_dim=256,
                 mask_obs_threshold=0.5, gradient_checkpointing=False):  # 新增梯度检查点参数
        super().__init__()
        self.d_model = d_model
        self.num_mamba_layers = num_mamba_layers
        self.gradient_checkpointing = gradient_checkpointing
        
        # TrackMambaUnit: 使用MambaBlock堆栈来处理单个轨迹
        self.track_mamba_unit = nn.ModuleList([
            MambaBlock(d_model, mamba_state_dim, mamba_expand, mamba_conv_dim)
            for _ in range(num_mamba_layers)
        ])
        
        # MambaStateSynchronizer: 轨迹间信息交互
        # 注意：这里的同步作用在 mamba_state 上，其维度为 d_inner
        d_inner = d_model * mamba_expand
        self.synchronizer = MambaStateSynchronizer(d_inner, n_head)

        # ================== 新增 ID 嵌入和预测头 ==================
        self.num_id_vocabulary = num_id_vocabulary
        # ID 词典 (参考 MOTIP)
        # +1 是为 MOTIP 论文中提到的"新生目标（newborn）"特殊 token 留出位置
        self.id_embed_head = nn.Embedding(self.num_id_vocabulary + 1, id_dim)

        # Prediction Heads
        self.bbox_head = MLP(d_model, d_model, 4, 3)
        # ID 预测头，输出对每个词典token的logits
        self.id_pred_head = MLP(d_model, d_model, self.num_id_vocabulary + 1, 3)

        # ================== 新增 MaskObs 阈值 ==================
        self.mask_obs_threshold = mask_obs_threshold
        # =====================================================
        # =========================================================

    def forward(self, track_instances):
        # ================== 准备融合了 ID 信息的输入 ==================
        # 获取当前轨迹的ID (obj_idxes)
        track_ids = track_instances.obj_idxes.clone().long()

        # 将-1（代表新生或未关联的query）映射到 "newborn" token 的索引
        newborn_mask = track_ids < 0
        newborn_token_idx = self.num_id_vocabulary
        track_ids[newborn_mask] = newborn_token_idx

        # 从ID词典中查找ID嵌入
        id_embeds = self.id_embed_head(track_ids)
        track_instances.id_embeddings = id_embeds # 更新

        # ================== 实现 MaskObs 策略 ==================
        # SambaMOTR 论文指出，屏蔽操作是基于置信度的。
        # 我们使用上一帧的预测分数 track_instances.scores
        conf_mask = track_instances.scores > self.mask_obs_threshold
        # 对于置信度低的观测，将其CIFE特征置零
        masked_cife_features = track_instances.cife_features * conf_mask.unsqueeze(-1)

        # 融合 CIFE 特征和 ID 嵌入
        fused_input = masked_cife_features + track_instances.id_embeddings
        # =====================================================
        # ============================================================
        
        # 获取Mamba状态
        hidden_states = track_instances.mamba_hidden_state # (K, L, D_inner, N) L是Mamba层数
        conv_histories = track_instances.mamba_conv_history # (K, L, C_dim, D_inner)

        # 逐层通过TrackMambaUnit和Synchronizer
        output_embed = fused_input
        for i, mamba_layer in enumerate(self.track_mamba_unit):
            if self.gradient_checkpointing and self.training:
                # 使用梯度检查点来节省显存
                def create_custom_forward(layer, sync_layer):
                    def custom_forward(embed, h_state, c_state):
                        embed_out, h_out, c_out = self.run_mamba_unit(
                            layer, embed.unsqueeze(1), h_state, c_state
                        )
                        embed_out = embed_out.squeeze(1)
                        synced_h = sync_layer(h_out)
                        return embed_out, synced_h, c_out
                    return custom_forward

                output_embed, synced_h, updated_c = torch.utils.checkpoint.checkpoint(
                    create_custom_forward(mamba_layer, self.synchronizer),
                    output_embed,
                    hidden_states[:, i, ...],
                    conv_histories[:, i, ...]
                )
                hidden_states[:, i, ...] = synced_h
                conv_histories[:, i, ...] = updated_c
            else:
                # 1. 单轨迹状态更新 (LTMU)
                # MambaBlock 需要 (B, Seq, Dim) 输入, 这里 B=K, Seq=1
                output_embed, updated_h, updated_c = self.run_mamba_unit(
                    mamba_layer,
                    output_embed.unsqueeze(1),
                    hidden_states[:, i, ...],
                    conv_histories[:, i, ...],
                )
                output_embed = output_embed.squeeze(1)

                # 2. 多轨迹状态同步 (MS)
                # 同步作用于隐状态 h
                synced_h = self.synchronizer(updated_h)

                # 3. 将更新和同步后的状态存回
                hidden_states[:, i, ...] = synced_h
                conv_histories[:, i, ...] = updated_c

        # ================== 使用新的预测头进行预测 ==================
        pred_boxes = self.bbox_head(output_embed)
        pred_id_logits = self.id_pred_head(output_embed) # 预测ID logits

        # 更新track_instances
        track_instances.pred_boxes = track_instances.ref_pts + pred_boxes
        track_instances.pred_logits = pred_id_logits # 使用新的ID logits
        # =========================================================
        track_instances.mamba_hidden_state = hidden_states
        track_instances.mamba_conv_history = conv_histories
        track_instances.output_embedding = output_embed

        return track_instances

    def run_mamba_unit(self, mamba_layer, input_seq, h, c):
        """
        MambaBlock的简化版前向传播，仅处理单步
        注意：这里的实现为了简化流程，与samba.py中的细节略有不同，但核心思想一致
        """

        # 残差
        residual = input_seq
        x = mamba_layer.norm(input_seq)

        # 投影
        x_and_res = mamba_layer.in_proj(x)
        x, res = x_and_res.split(split_size=[mamba_layer.d_inner, mamba_layer.d_inner], dim=-1)

        # 1D卷积
        # c (K, Conv_dim, D_inner)
        c = torch.roll(c, shifts=-1, dims=1)
        c[:, -1, :] = x.squeeze(1) # B=K, L=1
        x = torch.sum(c * mamba_layer.conv1d.weight.squeeze(1), dim=1).unsqueeze(1) # (K, 1, D_inner)
        x = F.silu(x)

        # SSM
        (d_in, n_state) = mamba_layer.A_log.shape
        A = -torch.exp(mamba_layer.A_log.float())
        D = mamba_layer.D.float()

        delta = F.softplus(mamba_layer.dt_proj(x))
        BC = mamba_layer.x_proj(x) # (K, 1, d_state * 2)
        B, C = BC.chunk(2, dim=-1) # 分割为B和C，每个都是 (K, 1, d_state)

        # 离散化
        deltaA = torch.exp(delta.unsqueeze(-1) * A)
        deltaB = delta.unsqueeze(-1) * B.unsqueeze(2)  # (K, 1, 1, d_state)

        # 状态更新
        h_new = deltaA.squeeze(1) * h + deltaB.squeeze(1).squeeze(1) * x.squeeze(1).unsqueeze(-1)
        y = torch.sum(h_new * C.squeeze(1).unsqueeze(1), dim=-1).unsqueeze(1) + x * D

        # 门控和输出
        y = y * F.silu(res)
        output = mamba_layer.out_proj(y) + residual

        return output, h_new, c
