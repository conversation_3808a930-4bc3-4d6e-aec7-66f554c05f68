# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-research. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from Deformable DETR (https://github.com/fundamentalvision/Deformable-DETR)
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

"""
DETR model and criterion classes.
"""
import copy
import math
import numpy as np
import torch
import torch.nn.functional as F
from torch import nn, Tensor
from typing import List

from util import box_ops, checkpoint
from util.misc import (NestedTensor, nested_tensor_from_tensor_list,
                       accuracy, get_world_size, interpolate, get_rank,
                       is_dist_avail_and_initialized, inverse_sigmoid)

from models.structures import Instances, Boxes, pairwise_iou, matched_boxlist_iou

from .backbone import build_backbone
from .matcher import build_matcher
# from .deformable_transformer_plus import build_deforamble_transformer, pos2posemb # 不再需要
# from .qim import build as build_query_interaction_layer # 不再需要
from .deformable_detr import SetCriterion, MLP, sigmoid_focal_loss
from .cife import CIFE  # 导入新的 CIFE 模块
from .cdiim import CDIIM # 导入新的CDIIM模块
import torchvision

def pos2posemb(pos, num_pos_feats=64, temperature=10000):
    """位置编码函数，从 deformable_transformer_plus.py 复制"""
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = temperature ** (2 * (dim_t // 2) / num_pos_feats)
    posemb = pos[..., None] / dim_t
    posemb = torch.stack((posemb[..., 0::2].sin(), posemb[..., 1::2].cos()), dim=-1).flatten(-3)
    return posemb


class ClipMatcher(SetCriterion):
    def __init__(self, num_classes,
                        matcher,
                        weight_dict,
                        losses,
                        num_id_vocabulary=50):
        """ Create the criterion.
        Parameters:
            num_classes: number of object categories, omitting the special no-object category
            matcher: module able to compute a matching between targets and proposals
            weight_dict: dict containing as key the names of the losses and as values their relative weight.
            eos_coef: relative classification weight applied to the no-object category
            losses: list of all the losses to be applied. See get_loss for list of available losses.
        """
        super().__init__(num_classes, matcher, weight_dict, losses)
        self.num_classes = num_classes
        self.matcher = matcher
        self.weight_dict = weight_dict
        self.losses = losses
        # ================== 新增ID损失相关 ==================
        self.num_id_vocabulary = num_id_vocabulary
        # ID损失的权重应由weight_dict控制，这里只定义损失函数
        self.id_loss_func = nn.CrossEntropyLoss()
        # ===============================================
        self.focal_loss = True
        self.losses_dict = {}
        self._current_frame_idx = 0

    def initialize_for_single_clip(self, gt_instances: List[Instances]):
        self.gt_instances = gt_instances
        self.num_samples = 0
        self.sample_device = None
        self._current_frame_idx = 0
        self.losses_dict = {}

    def _step(self):
        self._current_frame_idx += 1

    def calc_loss_for_track_scores(self, track_instances: Instances):
        frame_id = self._current_frame_idx - 1
        gt_instances = self.gt_instances[frame_id]
        outputs = {
            'pred_logits': track_instances.track_scores[None],
        }
        device = track_instances.track_scores.device

        num_tracks = len(track_instances)
        src_idx = torch.arange(num_tracks, dtype=torch.long, device=device)
        tgt_idx = track_instances.matched_gt_idxes  # -1 for FP tracks and disappeared tracks

        track_losses = self.get_loss('labels',
                                     outputs=outputs,
                                     gt_instances=[gt_instances],
                                     indices=[(src_idx, tgt_idx)],
                                     num_boxes=1)
        self.losses_dict.update(
            {'frame_{}_track_{}'.format(frame_id, key): value for key, value in
             track_losses.items()})

    def get_num_boxes(self, num_samples):
        num_boxes = torch.as_tensor(num_samples, dtype=torch.float, device=self.sample_device)
        if is_dist_avail_and_initialized():
            torch.distributed.all_reduce(num_boxes)
        num_boxes = torch.clamp(num_boxes / get_world_size(), min=1).item()
        return num_boxes

    def get_loss(self, loss, outputs, gt_instances, indices, num_boxes, **kwargs):
        loss_map = {
            'labels': self.loss_labels,
            'cardinality': self.loss_cardinality,
            'boxes': self.loss_boxes,
            'id': self.loss_id, # 注册新的ID损失
        }
        assert loss in loss_map, f'do you really want to compute {loss} loss?'
        return loss_map[loss](outputs, gt_instances, indices, num_boxes, **kwargs)

    def loss_boxes(self, outputs, gt_instances: List[Instances], indices: List[tuple], num_boxes):
        """Compute the losses related to the bounding boxes, the L1 regression loss and the GIoU loss
           targets dicts must contain the key "boxes" containing a tensor of dim [nb_target_boxes, 4]
           The target boxes are expected in format (center_x, center_y, h, w), normalized by the image size.
        """
        # We ignore the regression loss of the track-disappear slots.
        #TODO: Make this filter process more elegant.
        filtered_idx = []
        for src_per_img, tgt_per_img in indices:
            keep = tgt_per_img != -1
            filtered_idx.append((src_per_img[keep], tgt_per_img[keep]))
        indices = filtered_idx
        idx = self._get_src_permutation_idx(indices)
        src_boxes = outputs['pred_boxes'][idx]
        target_boxes = torch.cat([gt_per_img.boxes[i] for gt_per_img, (_, i) in zip(gt_instances, indices)], dim=0)

        # for pad target, don't calculate regression loss, judged by whether obj_id=-1
        target_obj_ids = torch.cat([gt_per_img.obj_ids[i] for gt_per_img, (_, i) in zip(gt_instances, indices)], dim=0) # size(16)
        mask = (target_obj_ids != -1)

        loss_bbox = F.l1_loss(src_boxes[mask], target_boxes[mask], reduction='none')
        loss_giou = 1 - torch.diag(box_ops.generalized_box_iou(
            box_ops.box_cxcywh_to_xyxy(src_boxes[mask]),
            box_ops.box_cxcywh_to_xyxy(target_boxes[mask])))

        losses = {}
        losses['loss_bbox'] = loss_bbox.sum() / num_boxes
        losses['loss_giou'] = loss_giou.sum() / num_boxes

        return losses

    def loss_labels(self, outputs, gt_instances: List[Instances], indices, num_boxes, log=False):
        """Classification loss (NLL)
        targets dicts must contain the key "labels" containing a tensor of dim [nb_target_boxes]
        """
        src_logits = outputs['pred_logits']
        idx = self._get_src_permutation_idx(indices)
        target_classes = torch.full(src_logits.shape[:2], self.num_classes,
                                    dtype=torch.int64, device=src_logits.device)
        # The matched gt for disappear track query is set -1.
        labels = []
        for gt_per_img, (_, J) in zip(gt_instances, indices):
            labels_per_img = torch.ones_like(J)
            # set labels of track-appear slots to 0.
            if len(gt_per_img) > 0:
                labels_per_img[J != -1] = gt_per_img.labels[J[J != -1]]
            labels.append(labels_per_img)
        target_classes_o = torch.cat(labels)
        target_classes[idx] = target_classes_o
        if self.focal_loss:
            gt_labels_target = F.one_hot(target_classes, num_classes=self.num_classes + 1)[:, :, :-1]  # no loss for the last (background) class
            gt_labels_target = gt_labels_target.to(src_logits)
            loss_ce = sigmoid_focal_loss(src_logits.flatten(1),
                                             gt_labels_target.flatten(1),
                                             alpha=0.25,
                                             gamma=2,
                                             num_boxes=num_boxes, mean_in_dim1=False)
            loss_ce = loss_ce.sum()
        else:
            loss_ce = F.cross_entropy(src_logits.transpose(1, 2), target_classes, self.empty_weight)
        losses = {'loss_ce': loss_ce}

        if log:
            # TODO this should probably be a separate loss, not hacked in this one here
            losses['class_error'] = 100 - accuracy(src_logits[idx], target_classes_o)[0]

        return losses

    # ================== 新增 loss_id 方法 ==================
    def loss_id(self, outputs, gt_instances: List[Instances], indices: List[tuple], num_boxes):
        """
        计算ID预测损失 (交叉熵)
        灵感来源: MOTIP.id_criterion.py
        """
        src_logits = outputs['pred_logits'] # 这是ID logits: (B, K, Num_ID_Vocab+1)
        idx = self._get_src_permutation_idx(indices)

        # 获取匹配上的GT的obj_ids
        target_ids = torch.cat([gt.obj_ids[J] for gt, (_, J) in zip(gt_instances, indices) if len(J) > 0])

        # 将全局唯一的obj_id映射到词典内部的ID
        # 注意: 这是一个简化的映射，实际项目中可能需要一个全局映射表
        # 这里我们假设obj_ids可以直接作为词典索引(需要预处理)
        # 在此我们做一个简单的取模映射作为示例
        target_classes = target_ids % self.num_id_vocabulary

        src_id_logits = src_logits[idx]

        loss_id = self.id_loss_func(src_id_logits, target_classes.long())

        losses = {'loss_id': loss_id}
        return losses
    # ====================================================

    def match_for_single_frame(self, outputs: dict):
        outputs_without_aux = {k: v for k, v in outputs.items() if k != 'aux_outputs'}

        gt_instances_i = self.gt_instances[self._current_frame_idx]  # gt instances of i-th image.
        # 确保 gt_instances_i 是 Instances 对象
        if isinstance(gt_instances_i, list) and len(gt_instances_i) > 0:
            gt_instances_i = gt_instances_i[0]
        track_instances: Instances = outputs_without_aux['track_instances']
        pred_logits_i = track_instances.pred_logits  # predicted logits of i-th image.
        pred_boxes_i = track_instances.pred_boxes  # predicted boxes of i-th image.

        obj_idxes = gt_instances_i.obj_ids
        outputs_i = {
            'pred_logits': pred_logits_i.unsqueeze(0),
            'pred_boxes': pred_boxes_i.unsqueeze(0),
        }

        # step1. inherit and update the previous tracks.
        num_disappear_track = 0
        track_instances.matched_gt_idxes[:] = -1
        i, j = torch.where(track_instances.obj_idxes[:, None] == obj_idxes)
        track_instances.matched_gt_idxes[i] = j

        full_track_idxes = torch.arange(len(track_instances), dtype=torch.long, device=pred_logits_i.device)
        matched_track_idxes = (track_instances.obj_idxes >= 0)  # occu 
        prev_matched_indices = torch.stack(
            [full_track_idxes[matched_track_idxes], track_instances.matched_gt_idxes[matched_track_idxes]], dim=1)

        # step2. select the unmatched slots.
        # note that the FP tracks whose obj_idxes are -2 will not be selected here.
        unmatched_track_idxes = full_track_idxes[track_instances.obj_idxes == -1]

        # step3. select the untracked gt instances (new tracks).
        tgt_indexes = track_instances.matched_gt_idxes
        tgt_indexes = tgt_indexes[tgt_indexes != -1]

        tgt_state = torch.zeros(len(gt_instances_i), device=pred_logits_i.device)
        tgt_state[tgt_indexes] = 1
        untracked_tgt_indexes = torch.arange(len(gt_instances_i), device=pred_logits_i.device)[tgt_state == 0]
        # untracked_tgt_indexes = select_unmatched_indexes(tgt_indexes, len(gt_instances_i))
        untracked_gt_instances = gt_instances_i[untracked_tgt_indexes]

        def match_for_single_decoder_layer(unmatched_outputs, matcher):
            new_track_indices = matcher(unmatched_outputs,
                                             [untracked_gt_instances])  # list[tuple(src_idx, tgt_idx)]

            src_idx = new_track_indices[0][0]
            tgt_idx = new_track_indices[0][1]
            # concat src and tgt.
            new_matched_indices = torch.stack([unmatched_track_idxes[src_idx], untracked_tgt_indexes[tgt_idx]],
                                              dim=1).to(pred_logits_i.device)
            return new_matched_indices

        # step4. do matching between the unmatched slots and GTs.
        unmatched_outputs = {
            'pred_logits': track_instances.pred_logits[unmatched_track_idxes].unsqueeze(0),
            'pred_boxes': track_instances.pred_boxes[unmatched_track_idxes].unsqueeze(0),
        }
        new_matched_indices = match_for_single_decoder_layer(unmatched_outputs, self.matcher)

        # step5. update obj_idxes according to the new matching result.
        track_instances.obj_idxes[new_matched_indices[:, 0]] = gt_instances_i.obj_ids[new_matched_indices[:, 1]].long()
        track_instances.matched_gt_idxes[new_matched_indices[:, 0]] = new_matched_indices[:, 1]

        # step6. calculate iou.
        active_idxes = (track_instances.obj_idxes >= 0) & (track_instances.matched_gt_idxes >= 0)
        active_track_boxes = track_instances.pred_boxes[active_idxes]
        if len(active_track_boxes) > 0:
            gt_boxes = gt_instances_i.boxes[track_instances.matched_gt_idxes[active_idxes]]
            active_track_boxes = box_ops.box_cxcywh_to_xyxy(active_track_boxes)
            gt_boxes = box_ops.box_cxcywh_to_xyxy(gt_boxes)
            track_instances.iou[active_idxes] = matched_boxlist_iou(Boxes(active_track_boxes), Boxes(gt_boxes))

        # step7. merge the unmatched pairs and the matched pairs.
        matched_indices = torch.cat([new_matched_indices, prev_matched_indices], dim=0)

        # step8. calculate losses.
        self.num_samples += len(gt_instances_i) + num_disappear_track
        self.sample_device = pred_logits_i.device
        for loss in self.losses:
            new_track_loss = self.get_loss(loss,
                                           outputs=outputs_i,
                                           gt_instances=[gt_instances_i],
                                           indices=[(matched_indices[:, 0], matched_indices[:, 1])],
                                           num_boxes=1)
            self.losses_dict.update(
                {'frame_{}_{}'.format(self._current_frame_idx, key): value for key, value in new_track_loss.items()})

        if 'aux_outputs' in outputs:
            for i, aux_outputs in enumerate(outputs['aux_outputs']):
                unmatched_outputs_layer = {
                    'pred_logits': aux_outputs['pred_logits'][0, unmatched_track_idxes].unsqueeze(0),
                    'pred_boxes': aux_outputs['pred_boxes'][0, unmatched_track_idxes].unsqueeze(0),
                }
                new_matched_indices_layer = match_for_single_decoder_layer(unmatched_outputs_layer, self.matcher)
                matched_indices_layer = torch.cat([new_matched_indices_layer, prev_matched_indices], dim=0)
                for loss in self.losses:
                    if loss == 'masks':
                        # Intermediate masks losses are too costly to compute, we ignore them.
                        continue
                    l_dict = self.get_loss(loss,
                                           aux_outputs,
                                           gt_instances=[gt_instances_i],
                                           indices=[(matched_indices_layer[:, 0], matched_indices_layer[:, 1])],
                                           num_boxes=1, )
                    self.losses_dict.update(
                        {'frame_{}_aux{}_{}'.format(self._current_frame_idx, i, key): value for key, value in
                         l_dict.items()})

        if 'ps_outputs' in outputs:
            for i, aux_outputs in enumerate(outputs['ps_outputs']):
                ar = torch.arange(len(gt_instances_i), device=obj_idxes.device)
                l_dict = self.get_loss('boxes',
                                        aux_outputs,
                                        gt_instances=[gt_instances_i],
                                        indices=[(ar, ar)],
                                        num_boxes=1, )
                self.losses_dict.update(
                    {'frame_{}_ps{}_{}'.format(self._current_frame_idx, i, key): value for key, value in
                        l_dict.items()})
        self._step()
        return track_instances

    def forward(self, outputs, input_data: dict):
        # losses of each frame are calculated during the model's forwarding and are outputted by the model as outputs['losses_dict].
        losses = outputs.pop("losses_dict")
        num_samples = self.get_num_boxes(self.num_samples)
        for loss_name, loss in losses.items():
            losses[loss_name] /= num_samples
        return losses


class RuntimeTrackerBase(object):
    def __init__(self, score_thresh=0.6, filter_score_thresh=0.5, miss_tolerance=10):
        self.score_thresh = score_thresh
        self.filter_score_thresh = filter_score_thresh
        self.miss_tolerance = miss_tolerance
        self.max_obj_id = 0

    def clear(self):
        self.max_obj_id = 0

    def update(self, track_instances: Instances):
        device = track_instances.obj_idxes.device

        track_instances.disappear_time[track_instances.scores >= self.score_thresh] = 0
        new_obj = (track_instances.obj_idxes == -1) & (track_instances.scores >= self.score_thresh)
        disappeared_obj = (track_instances.obj_idxes >= 0) & (track_instances.scores < self.filter_score_thresh)
        num_new_objs = new_obj.sum().item()

        track_instances.obj_idxes[new_obj] = self.max_obj_id + torch.arange(num_new_objs, device=device)
        self.max_obj_id += num_new_objs

        track_instances.disappear_time[disappeared_obj] += 1
        to_del = disappeared_obj & (track_instances.disappear_time >= self.miss_tolerance)
        track_instances.obj_idxes[to_del] = -1


class TrackerPostProcess(nn.Module):
    """ This module converts the model's output into the format expected by the coco api"""
    def __init__(self):
        super().__init__()

    @torch.no_grad()
    def forward(self, track_instances: Instances, target_size) -> Instances:
        """ Perform the computation
        Parameters:
            outputs: raw outputs of the model
            target_sizes: tensor of dimension [batch_size x 2] containing the size of each images of the batch
                          For evaluation, this must be the original image size (before any data augmentation)
                          For visualization, this should be the image size after data augment, but before padding
        """
        out_logits = track_instances.pred_logits
        out_bbox = track_instances.pred_boxes

        # prob = out_logits.sigmoid()
        scores = out_logits[..., 0].sigmoid()
        # scores, labels = prob.max(-1)

        # convert to [x0, y0, x1, y1] format
        boxes = box_ops.box_cxcywh_to_xyxy(out_bbox)
        # and from relative [0, 1] to absolute [0, height] coordinates
        img_h, img_w = target_size
        scale_fct = torch.Tensor([img_w, img_h, img_w, img_h]).to(boxes)
        boxes = boxes * scale_fct[None, :]

        track_instances.boxes = boxes
        track_instances.scores = scores
        track_instances.labels = torch.full_like(scores, 0)
        # track_instances.remove('pred_logits')
        # track_instances.remove('pred_boxes')
        return track_instances


def _get_clones(module, N):
    return nn.ModuleList([copy.deepcopy(module) for i in range(N)])


class MOTR(nn.Module):
    def __init__(self, backbone, num_classes, num_queries, criterion, # 移除了 transformer, track_embed, num_feature_levels 等参数
                 aux_loss=True, with_box_refine=False, two_stage=False, memory_bank=None, use_checkpoint=False, query_denoise=0,
                 # ================== 接收新参数 ==================
                 id_dim=256, mamba_state_dim=16, mamba_expand=2, mamba_num_layers=4, mamba_conv_dim=4, num_id_vocabulary=50):
        """ Initializes the model.
        Parameters:
            backbone: torch module of the backbone to be used. See backbone.py
            transformer: torch module of the transformer architecture. See transformer.py
            num_classes: number of object classes
            num_queries: number of object queries, ie detection slot. This is the maximal number of objects
                         DETR can detect in a single image. For COCO, we recommend 100 queries.
            aux_loss: True if auxiliary decoding losses (loss at each decoder layer) are to be used.
            with_box_refine: iterative bounding box refinement
            two_stage: two-stage Deformable DETR
        """
        super().__init__()
        self.num_queries = num_queries
        hidden_dim = self.hidden_dim = 256 # d_model 硬编码
        self.query_denoise = query_denoise  # 保存 query_denoise 参数

        # --- 存储 Mamba 参数 ---
        self.id_dim = id_dim
        self.mamba_state_dim = mamba_state_dim
        self.mamba_expand = mamba_expand
        self.mamba_num_layers = mamba_num_layers
        self.mamba_conv_dim = mamba_conv_dim
        self.num_id_vocabulary = num_id_vocabulary

        # --- 移除 Transformer 和 QIM, 替换为 CDIIM ---
        self.cdiim = CDIIM(
            d_model=hidden_dim,
            n_head=8, # 可以从args传入
            num_mamba_layers=mamba_num_layers,
            mamba_state_dim=mamba_state_dim,
            mamba_expand=mamba_expand,
            mamba_conv_dim=mamba_conv_dim,
            num_id_vocabulary=num_id_vocabulary,
            id_dim=id_dim,
            mask_obs_threshold=0.5  # 硬编码，后续可以从args传入
        )

        self.cife = CIFE(d_model=hidden_dim, n_layers=2, patch_size=8, state_dim=mamba_state_dim)
        self.cife_crop_size = (128, 64)

        self.num_classes = num_classes
        self.yolox_embed = nn.Embedding(1, hidden_dim)
        self.query_embed = nn.Embedding(num_queries, hidden_dim)
        self.position = nn.Embedding(num_queries, 4)
        if query_denoise:
            self.refine_embed = nn.Embedding(1, hidden_dim)

        # DETR的输入投影层仍然需要
        num_feature_levels = 4 # 硬编码或从args传入
        self.num_feature_levels = num_feature_levels
        if num_feature_levels > 1:
            num_backbone_outs = len(backbone.strides)
            input_proj_list = []
            for _ in range(num_backbone_outs):
                in_channels = backbone.num_channels[_]
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
            for _ in range(num_feature_levels - num_backbone_outs):
                input_proj_list.append(nn.Sequential(
                    nn.Conv2d(in_channels, hidden_dim, kernel_size=3, stride=2, padding=1),
                    nn.GroupNorm(32, hidden_dim),
                ))
                in_channels = hidden_dim
            self.input_proj = nn.ModuleList(input_proj_list)
        else:
            self.input_proj = nn.ModuleList([
                nn.Sequential(
                    nn.Conv2d(backbone.num_channels[0], hidden_dim, kernel_size=1),
                    nn.GroupNorm(32, hidden_dim),
                )])
        self.backbone = backbone
        self.aux_loss = aux_loss
        self.use_checkpoint = use_checkpoint

        # 移除与 Transformer Decoder 耦合的部分
        # self.class_embed = ...
        # self.bbox_embed = ...

        for proj in self.input_proj:
            nn.init.xavier_uniform_(proj[0].weight, gain=1)
            nn.init.constant_(proj[0].bias, 0)
        nn.init.uniform_(self.position.weight.data, 0, 1)
        self.post_process = TrackerPostProcess()
        self.track_base = RuntimeTrackerBase()
        self.criterion = criterion
        self.memory_bank = memory_bank
        self.mem_bank_len = 0 if memory_bank is None else memory_bank.max_his_length

    def _generate_empty_tracks(self, proposals=None):
        track_instances = Instances((1, 1))
        num_queries, d_model = self.query_embed.weight.shape  # (300, 512)
        device = self.query_embed.weight.device
        if proposals is None or len(proposals) == 0:
            track_instances.ref_pts = self.position.weight
            track_instances.query_pos = self.query_embed.weight
        else:
            # 确保 proposals 是张量
            if isinstance(proposals, list):
                if len(proposals) > 0 and isinstance(proposals[0], torch.Tensor):
                    proposals = proposals[0]  # 取第一个张量
                else:
                    # 如果 proposals 是空列表或不包含张量，使用默认值
                    track_instances.ref_pts = self.position.weight
                    track_instances.query_pos = self.query_embed.weight
                    return track_instances

            if proposals.numel() > 0:
                track_instances.ref_pts = torch.cat([self.position.weight, proposals[:, :4]])
                track_instances.query_pos = torch.cat([self.query_embed.weight, pos2posemb(proposals[:, 4:], d_model) + self.yolox_embed.weight])
            else:
                track_instances.ref_pts = self.position.weight
                track_instances.query_pos = self.query_embed.weight
        track_instances.output_embedding = torch.zeros((len(track_instances), d_model), device=device)
        track_instances.obj_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.matched_gt_idxes = torch.full((len(track_instances),), -1, dtype=torch.long, device=device)
        track_instances.disappear_time = torch.zeros((len(track_instances), ), dtype=torch.long, device=device)
        track_instances.iou = torch.ones((len(track_instances),), dtype=torch.float, device=device)
        track_instances.scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.track_scores = torch.zeros((len(track_instances),), dtype=torch.float, device=device)
        track_instances.pred_boxes = torch.zeros((len(track_instances), 4), dtype=torch.float, device=device)
        track_instances.pred_logits = torch.zeros((len(track_instances), self.num_classes), dtype=torch.float, device=device)

        mem_bank_len = self.mem_bank_len
        track_instances.mem_bank = torch.zeros((len(track_instances), mem_bank_len, d_model), dtype=torch.float32, device=device)
        track_instances.mem_padding_mask = torch.ones((len(track_instances), mem_bank_len), dtype=torch.bool, device=device)
        track_instances.save_period = torch.zeros((len(track_instances), ), dtype=torch.float32, device=device)

        # ================== 添加新字段初始化 (CIFE, ID, Mamba States) ==================
        n_tracks = len(track_instances)

        # CIFE 特征
        track_instances.cife_features = torch.zeros((n_tracks, d_model), dtype=torch.float32, device=device)

        # ID 嵌入 (参考 MOTIP)
        track_instances.id_embeddings = torch.zeros((n_tracks, self.id_dim), dtype=torch.float32, device=device)

        # Mamba 状态 (参考 SambaMOTR)
        mamba_hidden_state_shape = (n_tracks, self.mamba_num_layers, d_model * self.mamba_expand, self.mamba_state_dim)
        track_instances.mamba_hidden_state = torch.zeros(mamba_hidden_state_shape, dtype=torch.float32, device=device)

        mamba_conv_history_shape = (n_tracks, self.mamba_num_layers, self.mamba_conv_dim, d_model * self.mamba_expand)
        track_instances.mamba_conv_history = torch.zeros(mamba_conv_history_shape, dtype=torch.float32, device=device)
        # ===========================================================================

        return track_instances.to(self.query_embed.weight.device)

    def clear(self):
        self.track_base.clear()

    @torch.jit.unused
    def _set_aux_loss(self, outputs_class, outputs_coord):
        # this is a workaround to make torchscript happy, as torchscript
        # doesn't support dictionary with non-homogeneous values, such
        # as a dict having both a Tensor and a list.
        return [{'pred_logits': a, 'pred_boxes': b, }
                for a, b in zip(outputs_class[:-1], outputs_coord[:-1])]

    # _forward_single_image, _post_process_single_image 和 inference_single_image 方法
    # 已被 CDIIM 替代，不再需要，但保留 _set_aux_loss 以防需要

    def forward(self, data: dict):
        if self.training:
            self.criterion.initialize_for_single_clip(data['gt_instances'])
        frames = data['imgs']  # list of Tensor.
        outputs = {
            'pred_logits': [],
            'pred_boxes': [],
        }
        track_instances = None
        keys = list(self._generate_empty_tracks()._fields.keys())
        for frame_index, (frame, gt, proposals) in enumerate(zip(frames, data['gt_instances'], data['proposals'])):
            # 确保 frame 是张量
            if isinstance(frame, list):
                frame = frame[0] if len(frame) > 0 else frame
            if hasattr(frame, 'requires_grad'):
                frame.requires_grad = False
            is_last = frame_index == len(frames) - 1

            if self.query_denoise > 0:
                l_1 = l_2 = self.query_denoise
                # 确保 gt 是 Instances 对象
                if hasattr(gt, 'boxes'):
                    gtboxes = gt.boxes.clone()
                    _rs = torch.rand_like(gtboxes) * 2 - 1
                    gtboxes[..., :2] += gtboxes[..., 2:] * _rs[..., :2] * l_1
                    gtboxes[..., 2:] *= 1 + l_2 * _rs[..., 2:]
                else:
                    gtboxes = None
            else:
                gtboxes = None

            if track_instances is None:
                track_instances = self._generate_empty_tracks(proposals)
            else:
                track_instances = Instances.cat([
                    self._generate_empty_tracks(proposals),
                    track_instances])

            # ================== CIFE 特征提取 ==================
            # 1. 准备所有需要提取特征的 boxes
            # 注意: boxes 格式是 cxcywh (normalized)
            if hasattr(track_instances, 'pred_boxes') and track_instances.pred_boxes.numel() > 0:
                boxes_to_crop_norm = track_instances.pred_boxes
            else:
                boxes_to_crop_norm = track_instances.ref_pts
            boxes_to_crop_xyxy_norm = box_ops.box_cxcywh_to_xyxy(boxes_to_crop_norm)

            # 2. 从图像中裁剪图像块 (crops)
            # roi_align 需要 [N, 4] 的 boxes, 格式为 (x1, y1, x2, y2)
            # 还需要一个索引来指定每个box属于哪个batch_img (这里只有一个图像，所以都是0)
            # 处理 NestedTensor 和普通 Tensor
            if hasattr(frame, 'tensors'):
                h, w = frame.tensors.shape[-2:]
            else:
                h, w = frame.shape[-2:]
            # 处理 NestedTensor 和普通 Tensor 的 device
            if hasattr(frame, 'tensors'):
                device = frame.tensors.device
            else:
                device = frame.device
            scale_fct = torch.tensor([w, h, w, h], device=device)
            boxes_to_crop_xyxy_pixel = boxes_to_crop_xyxy_norm * scale_fct

            # roi_align的输入需要(batch_idx, x1, y1, x2, y2)格式
            if len(boxes_to_crop_xyxy_pixel) > 0:
                box_indices = torch.full((len(boxes_to_crop_xyxy_pixel), 1), 0, device=device, dtype=torch.float32)
                rois = torch.cat([box_indices, boxes_to_crop_xyxy_pixel], dim=1)

                # 使用 roi_align 裁剪图像，并指定输出尺寸
                # frame 需要是 (B, C, H, W) 格式，这里 B=1
                # 处理 NestedTensor 和普通 Tensor 的 unsqueeze
            if hasattr(frame, 'tensors'):
                frame_tensor = frame.tensors
            else:
                frame_tensor = frame

            # 确保 frame_tensor 有正确的批次维度
            if frame_tensor.dim() == 3:  # (C, H, W)
                frame_tensor = frame_tensor.unsqueeze(0)  # (1, C, H, W)

            image_crops = torchvision.ops.roi_align(frame_tensor, rois, output_size=self.cife_crop_size, spatial_scale=1.0)

            # 3. 调用 CIFE 模块提取特征
            if image_crops.shape[0] > 0:
                cife_features = self.cife(image_crops)
                track_instances.cife_features = cife_features
            # ======================================================

            # 2. CDIIM 时序建模与预测
            # CDIIM模块会直接更新track_instances内部的预测结果和Mamba状态
            track_instances = self.cdiim(track_instances)

            # 3. 损失计算与匹配 (为下一阶段准备)
            # 输出字典，用于后续的损失计算
            frame_res = {'pred_logits': track_instances.pred_logits.unsqueeze(0),
                         'pred_boxes': track_instances.pred_boxes.unsqueeze(0),
                         'track_instances': track_instances}

            if self.training:
                # 匹配和损失计算逻辑保持在criterion中，但输入源变了
                track_instances = self.criterion.match_for_single_frame(frame_res)
            else:
                self.track_base.update(track_instances)

            # 准备下一帧的输入 (QIM的功能被CDIIM取代)
            if not is_last:
                # 这里可以添加一个简单的特征更新逻辑，或者让CDIIM的输出直接作为下一帧的输入
                # 为了简化，我们假设CDIIM的输出 embedding 已经是下一帧的 query embedding
                track_instances.query_pos = track_instances.output_embedding
            else:
                track_instances = None

            outputs['pred_logits'].append(frame_res['pred_logits'])
            outputs['pred_boxes'].append(frame_res['pred_boxes'])

        if not self.training:
            outputs['track_instances'] = track_instances
        else:
            outputs['losses_dict'] = self.criterion.losses_dict
        return outputs


def build(args):
    dataset_to_num_classes = {
        'coco': 91,
        'coco_panoptic': 250,
        'e2e_mot': 1,
        'e2e_dance': 1,
        'e2e_joint': 1,
        'e2e_static_mot': 1,
    }
    assert args.dataset_file in dataset_to_num_classes
    num_classes = dataset_to_num_classes[args.dataset_file]
    device = torch.device(args.device)

    backbone = build_backbone(args)

    # transformer = build_deforamble_transformer(args) # 移除
    # d_model = transformer.d_model # 移除
    # hidden_dim = args.dim_feedforward # 移除
    # query_interaction_layer = build_query_interaction_layer(...) # 移除

    img_matcher = build_matcher(args)
    num_frames_per_batch = max(args.sampler_lengths)
    weight_dict = {}
    for i in range(num_frames_per_batch):
        weight_dict.update({
            # "frame_{}_loss_ce".format(i): args.cls_loss_coef, # 原有的cls loss权重可以降低或移除
            'frame_{}_loss_bbox'.format(i): args.bbox_loss_coef,
            'frame_{}_loss_giou'.format(i): args.giou_loss_coef,
            # ============= 新增ID损失权重 =============
            'frame_{}_loss_id'.format(i): args.id_loss_coef,
        })

    # TODO this is a hack
    if args.aux_loss:
        for i in range(num_frames_per_batch):
            for j in range(args.dec_layers - 1):
                weight_dict.update({"frame_{}_aux{}_loss_ce".format(i, j): args.cls_loss_coef,
                                    'frame_{}_aux{}_loss_bbox'.format(i, j): args.bbox_loss_coef,
                                    'frame_{}_aux{}_loss_giou'.format(i, j): args.giou_loss_coef,
                                    })
            for j in range(args.dec_layers):
                weight_dict.update({"frame_{}_ps{}_loss_ce".format(i, j): args.cls_loss_coef,
                                    'frame_{}_ps{}_loss_bbox'.format(i, j): args.bbox_loss_coef,
                                    'frame_{}_ps{}_loss_giou'.format(i, j): args.giou_loss_coef,
                                    })
    # if args.memory_bank_type is not None and len(args.memory_bank_type) > 0:
    #     memory_bank = build_memory_bank(args, d_model, hidden_dim, d_model * 2)
    #     for i in range(num_frames_per_batch):
    #         weight_dict.update({"frame_{}_track_loss_ce".format(i): args.cls_loss_coef})
    # else:
    memory_bank = None
    losses = ['boxes', 'id'] # 更新损失列表
    criterion = ClipMatcher(num_classes, matcher=img_matcher, weight_dict=weight_dict, losses=losses, num_id_vocabulary=args.num_id_vocabulary)
    criterion.to(device)
    postprocessors = {}
    model = MOTR(
        backbone,
        # transformer, # 移除
        # track_embed=query_interaction_layer, # 移除
        # num_feature_levels=args.num_feature_levels, # 移除
        num_classes=num_classes,
        num_queries=args.num_queries,
        criterion=criterion,
        aux_loss=args.aux_loss,
        # with_box_refine=args.with_box_refine, # 移除
        # two_stage=args.two_stage, # 移除
        memory_bank=memory_bank,
        use_checkpoint=args.use_checkpoint,
        query_denoise=args.query_denoise,
        # ================== 传递新参数 ==================
        id_dim=args.id_dim,
        mamba_state_dim=args.mamba_state_dim,
        mamba_expand=args.mamba_expand,
        mamba_num_layers=args.mamba_num_layers,
        mamba_conv_dim=args.mamba_conv_dim,
        num_id_vocabulary=args.num_id_vocabulary,
        # ===============================================
    )
    return model, criterion, postprocessors
