#!/usr/bin/env python3
"""
CIM-Tracker 数据集设置脚本
帮助用户正确设置 DanceTrack 数据集
"""

import os
import sys
import argparse
from pathlib import Path
import urllib.request
import zipfile
import shutil

def download_file(url, destination):
    """下载文件并显示进度"""
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, downloaded * 100 / total_size)
            sys.stdout.write(f"\r下载进度: {percent:.1f}% ({downloaded}/{total_size} bytes)")
            sys.stdout.flush()
    
    print(f"正在下载: {url}")
    urllib.request.urlretrieve(url, destination, progress_hook)
    print("\n下载完成!")

def setup_dancetrack(data_dir):
    """设置 DanceTrack 数据集"""
    print("🎯 设置 DanceTrack 数据集...")
    
    # 创建数据目录
    dancetrack_dir = Path(data_dir) / "DanceTrack"
    dancetrack_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ 创建数据目录: {dancetrack_dir}")
    
    # 检查是否已有数据
    train_dir = dancetrack_dir / "train"
    if train_dir.exists() and len(list(train_dir.glob("*"))) > 0:
        print("⚠️  检测到已存在的 DanceTrack 数据")
        response = input("是否重新下载? (y/N): ")
        if response.lower() != 'y':
            print("跳过下载，使用现有数据")
            return True
    
    # 提供下载指导
    print("\n📥 DanceTrack 数据集下载指导:")
    print("1. 访问官方网站: https://github.com/DanceTrack/DanceTrack")
    print("2. 下载数据集文件")
    print("3. 解压到以下目录结构:")
    print(f"""
{dancetrack_dir}/
├── train/
│   ├── dancetrack0001/
│   │   ├── img1/
│   │   └── gt/
│   └── ...
├── val/
└── test/
    """)
    
    # 检查用户是否已手动下载
    response = input("\n是否已手动下载并解压数据集? (y/N): ")
    if response.lower() == 'y':
        return verify_dancetrack_structure(dancetrack_dir)
    
    print("请手动下载数据集后重新运行此脚本")
    return False

def verify_dancetrack_structure(dancetrack_dir):
    """验证 DanceTrack 数据集结构"""
    print("🔍 验证数据集结构...")
    
    required_dirs = ["train", "val", "test"]
    for dir_name in required_dirs:
        dir_path = dancetrack_dir / dir_name
        if not dir_path.exists():
            print(f"❌ 缺少目录: {dir_path}")
            return False
        
        # 检查是否有视频序列
        sequences = list(dir_path.glob("dancetrack*"))
        if len(sequences) == 0:
            print(f"❌ {dir_name} 目录中没有找到视频序列")
            return False
        
        print(f"✅ {dir_name}: 找到 {len(sequences)} 个视频序列")
        
        # 检查第一个序列的结构
        first_seq = sequences[0]
        img1_dir = first_seq / "img1"
        gt_dir = first_seq / "gt"
        
        if not img1_dir.exists():
            print(f"❌ 缺少图像目录: {img1_dir}")
            return False
            
        if dir_name == "train" and not gt_dir.exists():
            print(f"❌ 缺少标注目录: {gt_dir}")
            return False
        
        # 检查图像文件
        images = list(img1_dir.glob("*.jpg"))
        if len(images) == 0:
            print(f"❌ {img1_dir} 中没有找到图像文件")
            return False
        
        print(f"✅ {first_seq.name}: 找到 {len(images)} 张图像")
    
    print("🎉 数据集结构验证通过!")
    return True

def download_pretrained_weights(pretrained_dir):
    """下载预训练权重"""
    print("🔧 设置预训练权重...")

    pretrained_dir = Path(pretrained_dir)
    pretrained_dir.mkdir(parents=True, exist_ok=True)

    weight_file = pretrained_dir / "r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth"

    if weight_file.exists():
        print("✅ DETR 预训练权重已存在")
        return True

    print("📥 DETR 预训练权重下载指导:")
    print("DETR 权重用于初始化 backbone 和基础特征提取组件")
    print("1. 访问: https://github.com/fundamentalvision/Deformable-DETR")
    print("2. 下载: r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth")
    print(f"3. 保存到: {weight_file}")

    response = input("\n是否已手动下载 DETR 预训练权重? (y/N): ")
    if response.lower() == 'y':
        if weight_file.exists():
            print("✅ DETR 预训练权重设置完成")
            return True
        else:
            print(f"❌ 未找到权重文件: {weight_file}")
            return False

    print("请手动下载 DETR 预训练权重后重新运行此脚本")
    return False

def download_yolox_detections(data_dir):
    """下载 YOLOX 检测结果（可选）"""
    print("🎯 设置 YOLOX 检测结果...")

    det_db_file = Path(data_dir) / "det_db_motrv2.json"

    if det_db_file.exists():
        print("✅ YOLOX 检测结果已存在")
        return True

    print("📥 YOLOX 检测结果下载指导:")
    print("YOLOX 检测结果用作额外的 proposals 来增强训练效果")
    print("1. 访问: https://github.com/megvii-research/MOTRv2")
    print("2. 下载: det_db_motrv2.json")
    print(f"3. 保存到: {det_db_file}")
    print("注意: 这是可选的，没有也可以正常训练")

    response = input("\n是否已下载 YOLOX 检测结果? (y/N): ")
    if response.lower() == 'y':
        if det_db_file.exists():
            print("✅ YOLOX 检测结果设置完成")
            return True
        else:
            print(f"❌ 未找到检测结果文件: {det_db_file}")
            return False
    else:
        print("⚠️  跳过 YOLOX 检测结果，将使用空的检测数据库")
        # 创建空的检测数据库
        import json
        with open(det_db_file, 'w') as f:
            json.dump({}, f)
        return True

def create_det_db(data_dir):
    """创建检测数据库文件"""
    print("📝 创建检测数据库...")
    
    det_db_file = Path(data_dir) / "det_db_cim_tracker.json"
    
    if det_db_file.exists():
        print("✅ 检测数据库已存在")
        return True
    
    # 创建空的检测数据库
    import json
    empty_db = {}
    
    with open(det_db_file, 'w') as f:
        json.dump(empty_db, f)
    
    print(f"✅ 创建检测数据库: {det_db_file}")
    return True

def update_config_paths(config_file, data_dir, pretrained_dir):
    """更新配置文件中的路径"""
    print("⚙️  更新配置文件路径...")
    
    config_path = Path(config_file)
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 更新路径
    updated_lines = []
    for line in lines:
        if line.startswith('--mot_path'):
            updated_lines.append(f'--mot_path {data_dir}\n')
        elif line.startswith('--pretrained'):
            pretrained_file = Path(pretrained_dir) / "r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth"
            updated_lines.append(f'--pretrained {pretrained_file}\n')
        elif line.startswith('--det_db'):
            det_db_file = Path(data_dir) / "det_db_motrv2.json"
            updated_lines.append(f'--det_db {det_db_file}\n')
        else:
            updated_lines.append(line)
    
    # 写回配置文件
    with open(config_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print(f"✅ 配置文件已更新: {config_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='CIM-Tracker 数据集设置脚本')
    parser.add_argument('--data_dir', default='data', help='数据目录路径')
    parser.add_argument('--pretrained_dir', default='pretrained', help='预训练权重目录路径')
    parser.add_argument('--config_file', default='configs/cim_tracker_dancetrack.args', help='配置文件路径')
    
    args = parser.parse_args()
    
    print("🚀 CIM-Tracker 数据集设置开始...")
    print("=" * 60)
    
    success = True
    
    # 1. 设置 DanceTrack 数据集
    if not setup_dancetrack(args.data_dir):
        success = False
    
    print("\n" + "=" * 60)
    
    # 2. 下载预训练权重
    if not download_pretrained_weights(args.pretrained_dir):
        success = False

    print("\n" + "=" * 60)

    # 3. 下载 YOLOX 检测结果
    if not download_yolox_detections(args.data_dir):
        success = False
    
    print("\n" + "=" * 60)
    
    # 4. 更新配置文件
    if not update_config_paths(args.config_file, args.data_dir, args.pretrained_dir):
        success = False
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 数据集设置完成!")
        print("\n📋 下一步:")
        print(f"1. 检查配置文件: {args.config_file}")
        print("2. 开始训练:")
        print(f"   python main.py $(cat {args.config_file})")
    else:
        print("❌ 数据集设置失败，请检查上述错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
