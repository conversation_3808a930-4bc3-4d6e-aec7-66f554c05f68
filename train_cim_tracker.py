#!/usr/bin/env python3
"""
CIM-Tracker 训练启动脚本
简化的训练接口，自动检查环境和配置
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_environment():
    """检查训练环境"""
    print("🔍 检查训练环境...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ Python 版本过低，需要 Python >= 3.8")
        return False
    print(f"✅ Python 版本: {sys.version}")
    
    # 检查 PyTorch
    try:
        import torch
        print(f"✅ PyTorch 版本: {torch.__version__}")
        if torch.cuda.is_available():
            print(f"✅ CUDA 可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA 不可用，将使用 CPU 训练（速度较慢）")
    except ImportError:
        print("❌ PyTorch 未安装")
        return False
    
    # 检查其他依赖
    required_packages = ['numpy', 'opencv-python', 'pillow', 'einops']
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            return False
    
    return True

def check_data_and_config(config_file, data_dir, pretrained_dir):
    """检查数据和配置"""
    print("📁 检查数据和配置...")
    
    # 检查配置文件
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件: {config_file}")
    
    # 检查数据目录
    dancetrack_dir = Path(data_dir) / "DanceTrack"
    if not dancetrack_dir.exists():
        print(f"❌ DanceTrack 数据集不存在: {dancetrack_dir}")
        print("请运行: python setup_dataset.py")
        return False
    
    # 检查训练数据
    train_dir = dancetrack_dir / "train"
    if not train_dir.exists() or len(list(train_dir.glob("dancetrack*"))) == 0:
        print(f"❌ 训练数据不存在: {train_dir}")
        return False
    print(f"✅ 训练数据: {len(list(train_dir.glob('dancetrack*')))} 个序列")
    
    # 检查预训练权重（根据数据集类型）
    dataset_name = Path(config_file).stem.split('_')[-1]  # 从配置文件名提取数据集名
    pretrained_file = Path(pretrained_dir) / f"r50_deformable_detr_coco_{dataset_name}.pth"
    if not pretrained_file.exists():
        print(f"❌ 预训练权重不存在: {pretrained_file}")
        print("请确保预训练权重在正确位置")
        return False
    print(f"✅ 预训练权重: {pretrained_file}")
    
    return True

def update_config_for_current_env(config_file, data_dir, pretrained_dir, output_dir):
    """为当前环境更新配置文件"""
    print("⚙️  更新配置文件...")
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 更新路径
    updated_lines = []
    for line in lines:
        line = line.strip()
        if line.startswith('--mot_path'):
            updated_lines.append(f'--mot_path {data_dir}\n')
        elif line.startswith('--pretrained'):
            pretrained_file = Path(pretrained_dir) / "r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth"
            updated_lines.append(f'--pretrained {pretrained_file}\n')
        elif line.startswith('--output_dir'):
            updated_lines.append(f'--output_dir {output_dir}\n')
        elif line.startswith('--det_db'):
            det_db_file = Path(data_dir) / "det_db_motrv2.json"
            updated_lines.append(f'--det_db {det_db_file}\n')
        else:
            updated_lines.append(line + '\n')
    
    # 写回临时配置文件
    temp_config = Path(config_file).parent / f"temp_{Path(config_file).name}"
    with open(temp_config, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print(f"✅ 临时配置文件: {temp_config}")
    return temp_config

def start_training(config_file, num_gpus=1, resume=None):
    """启动训练"""
    print(f"🚀 启动训练 (使用 {num_gpus} 个GPU)...")
    
    # 构建训练命令
    if num_gpus == 1:
        cmd = [
            sys.executable, "main.py"
        ]
        # 添加配置文件参数
        with open(config_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    cmd.append(line)
    else:
        cmd = [
            sys.executable, "-m", "torch.distributed.launch",
            f"--nproc_per_node={num_gpus}",
            "--use_env",
            "main.py"
        ]
        # 添加配置文件参数
        with open(config_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    cmd.append(line)
    
    # 添加恢复训练参数
    if resume:
        cmd.extend(["--resume", resume])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    # 启动训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='CIM-Tracker 训练启动脚本')
    parser.add_argument('--config', default='configs/cim_tracker_dancetrack.args',
                       help='配置文件路径 (dancetrack/sportsmot/bft)')
    parser.add_argument('--dataset', choices=['dancetrack', 'sportsmot', 'bft'],
                       default='dancetrack', help='数据集类型')
    parser.add_argument('--data_dir', default='data',
                       help='数据目录路径')
    parser.add_argument('--pretrained_dir', default='D:/Projects/Datasets/CIM_models',
                       help='预训练权重目录路径')
    parser.add_argument('--output_dir', default=None,
                       help='输出目录路径 (如果不指定，将根据数据集自动设置)')
    parser.add_argument('--num_gpus', type=int, default=1,
                       help='使用的GPU数量')
    parser.add_argument('--resume', default=None,
                       help='恢复训练的检查点路径')
    parser.add_argument('--skip_checks', action='store_true',
                       help='跳过环境检查')
    
    args = parser.parse_args()

    # 自动设置配置文件和输出目录
    if args.config == 'configs/cim_tracker_dancetrack.args':
        config_map = {
            'dancetrack': 'configs/cim_tracker_dancetrack.args',
            'sportsmot': 'configs/cim_tracker_sportsmot.args',
            'bft': 'configs/cim_tracker_bft.args'
        }
        args.config = config_map[args.dataset]

    if args.output_dir is None:
        args.output_dir = f'outputs/cim_tracker_{args.dataset}'

    print(f"🎯 CIM-Tracker 训练启动 - {args.dataset.upper()} 数据集")
    print("=" * 60)
    print(f"📁 配置文件: {args.config}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📁 预训练权重目录: {args.pretrained_dir}")
    
    # 检查环境
    if not args.skip_checks:
        if not check_environment():
            print("❌ 环境检查失败")
            sys.exit(1)
        
        print("\n" + "=" * 60)
        
        if not check_data_and_config(args.config, args.data_dir, args.pretrained_dir):
            print("❌ 数据和配置检查失败")
            sys.exit(1)
    
    print("\n" + "=" * 60)
    
    # 创建输出目录
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    
    # 更新配置文件
    temp_config = update_config_for_current_env(
        args.config, args.data_dir, args.pretrained_dir, args.output_dir
    )
    
    print("\n" + "=" * 60)
    
    # 启动训练
    success = start_training(temp_config, args.num_gpus, args.resume)
    
    # 清理临时文件
    if temp_config.exists():
        temp_config.unlink()
    
    if success:
        print("\n🎉 训练完成!")
    else:
        print("\n❌ 训练失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
