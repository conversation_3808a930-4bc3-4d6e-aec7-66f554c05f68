_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.11.13
        t:
            "1":
                - 1
                - 41
                - 51
            "2":
                - 1
                - 41
                - 51
            "3":
                - 13
                - 15
                - 16
                - 55
            "4": 3.11.13
            "5": 0.20.1
            "8":
                - 3
            "12": 0.20.1
            "13": windows-amd64
accurate_ratio:
    value: false
append_crowd:
    value: false
aug_occlusion_prob:
    value: 0.1
aug_switch_prob:
    value: 0.1
aux_loss:
    value: true
backbone:
    value: resnet50
batch_size:
    value: 1
bbox_loss_coef:
    value: 5
cache_mode:
    value: false
cj:
    value: false
clip_max_norm:
    value: 0.1
cls_loss_coef:
    value: 2
coco_panoptic_path:
    value: null
coco_path:
    value: /data/workspace/detectron2/datasets/coco/
crop:
    value: false
data_txt_path_train:
    value: ./datasets/data_path/detmot17.train
data_txt_path_val:
    value: ./datasets/data_path/detmot17.train
dataset_file:
    value: e2e_dance
dec_layers:
    value: 6
dec_n_points:
    value: 4
decoder_cross_self:
    value: false
det_db:
    value: D:/Projects/Datasets/CIM_models/det_db_motrv2.json
device:
    value: cuda
dice_loss_coef:
    value: 1
dilation:
    value: false
dim_feedforward:
    value: 1024
distributed:
    value: false
dropout:
    value: 0.1
enable_fpn:
    value: false
enc_layers:
    value: 6
enc_n_points:
    value: 4
epochs:
    value: 20
eval:
    value: false
exp_name:
    value: submit
extra_track_attn:
    value: false
filter_ignore:
    value: false
focal_alpha:
    value: 0.25
fp_ratio:
    value: 0
frozen_weights:
    value: null
giou_loss_coef:
    value: 2
grad_frames:
    value: 5
gt_file_train:
    value: null
gt_file_val:
    value: null
hidden_dim:
    value: 256
id_dim:
    value: 256
id_loss_coef:
    value: 2
img_path:
    value: data/valid/JPEGImages/
input_video:
    value: figs/demo.mp4
loss_normalizer:
    value: false
lr:
    value: 0.0002
lr_backbone:
    value: 2e-05
lr_backbone_names:
    value:
        - backbone.0
lr_drop:
    value: 15
lr_drop_epochs:
    value: null
lr_linear_proj_mult:
    value: 0.1
lr_linear_proj_names:
    value:
        - reference_points
        - sampling_offsets
lr_scheduler:
    value: cosine
mamba_conv_dim:
    value: 4
mamba_expand:
    value: 2
mamba_num_layers:
    value: 4
mamba_state_dim:
    value: 16
mask_loss_coef:
    value: 1
mask_obs_threshold:
    value: 0.5
masks:
    value: false
max_size:
    value: 1333
memory_bank_len:
    value: 4
memory_bank_score_thresh:
    value: 0
memory_bank_type:
    value: null
memory_bank_with_self_attn:
    value: false
merger_dropout:
    value: 0.1
meta_arch:
    value: motr
mix_match:
    value: false
mot_path:
    value: D:/Projects/Datasets
nheads:
    value: 8
num_anchors:
    value: 1
num_feature_levels:
    value: 4
num_id_vocabulary:
    value: 50
num_queries:
    value: 10
num_workers:
    value: 0
output_dir:
    value: outputs/cim_tracker_dancetrack
position_embedding:
    value: sine
position_embedding_scale:
    value: 6.***************
pretrained:
    value: D:/Projects/Datasets/CIM_models/r50_deformable_detr_coco_dancetrack.pth
query_denoise:
    value: 0.1
query_interaction_layer:
    value: QIM
random_drop:
    value: 0
remove_difficult:
    value: false
resume:
    value: ""
sample_interval:
    value: 10
sample_mode:
    value: random_interval
sampler_lengths:
    value:
        - 5
sampler_steps:
    value: null
save_period:
    value: 50
seed:
    value: 42
set_cost_bbox:
    value: 5
set_cost_class:
    value: 2
set_cost_giou:
    value: 2
sgd:
    value: false
sigmoid_attn:
    value: false
start_epoch:
    value: 0
two_stage:
    value: false
update_query_pos:
    value: false
use_checkpoint:
    value: true
val_width:
    value: 800
vis:
    value: false
weight_decay:
    value: 0.0001
with_box_refine:
    value: true
